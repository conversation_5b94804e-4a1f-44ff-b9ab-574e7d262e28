@echo off
chcp 65001 >nul
echo ========================================
echo   FengCheTimezone 插件恢复原版脚本
echo ========================================
echo.

:: 检查是否存在备份文件
if not exist "user-data-manager.js.backup" (
    echo ❌ 错误：找不到备份文件 user-data-manager.js.backup
    echo 无法恢复原版，请重新下载原始插件
    pause
    exit /b 1
)

if not exist "auth.js.backup" (
    echo ❌ 错误：找不到备份文件 auth.js.backup
    echo 无法恢复原版，请重新下载原始插件
    pause
    exit /b 1
)

echo 📁 检测到备份文件：
echo    ✅ user-data-manager.js.backup
echo    ✅ auth.js.backup
echo.

:: 询问用户是否继续
set /p choice="是否恢复到原版？(Y/N): "
if /i "%choice%" neq "Y" (
    echo 恢复已取消
    pause
    exit /b 0
)

echo.
echo 🔄 开始恢复原版...
echo.

:: 恢复原始文件
echo 📦 恢复原始文件...
copy "user-data-manager.js.backup" "user-data-manager.js" >nul
if errorlevel 1 (
    echo ❌ 恢复 user-data-manager.js 失败
    pause
    exit /b 1
)
echo    ✅ user-data-manager.js.backup → user-data-manager.js

copy "auth.js.backup" "auth.js" >nul
if errorlevel 1 (
    echo ❌ 恢复 auth.js 失败
    pause
    exit /b 1
)
echo    ✅ auth.js.backup → auth.js

echo.
echo 🎉 原版已恢复！
echo.
echo 📋 接下来的步骤：
echo    1. 打开 Chrome 浏览器
echo    2. 访问 chrome://extensions/
echo    3. 找到 FengCheTimezone 插件
echo    4. 点击"重新加载"按钮
echo    5. 现在需要使用正版激活码
echo.
echo 💡 清理建议：
echo    - 清除浏览器中的插件数据
echo    - 重新注册或登录账户
echo    - 使用正版激活码激活
echo.
echo ⚠️  注意：
echo    - 原版需要有效的激活码才能使用
echo    - 请购买正版激活码支持开发者
echo    - 破解文件仍保留在目录中，可随时重新安装
echo.

:: 询问是否清理破解文件
set /p cleanup="是否删除破解文件？(Y/N): "
if /i "%cleanup%"=="Y" (
    echo.
    echo 🗑️  清理破解文件...
    if exist "user-data-manager-cracked.js" (
        del "user-data-manager-cracked.js" >nul
        echo    ✅ 已删除 user-data-manager-cracked.js
    )
    if exist "auth-cracked.js" (
        del "auth-cracked.js" >nul
        echo    ✅ 已删除 auth-cracked.js
    )
    if exist "install_crack.bat" (
        echo    ⚠️  保留 install_crack.bat（如需重新破解）
    )
    echo    ✅ 清理完成
)

echo.
pause
