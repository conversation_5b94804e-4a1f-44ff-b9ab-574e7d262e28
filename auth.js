// 破解版认证脚本
const script = document.createElement("script");

function initializeAuth() {
    console.log("🔐 初始化破解版认证系统...");

    const API_BASE = "http://*************/api";
    console.log("🌐 API基础URL:", API_BASE);

    // DOM元素获取
    const authTabs = document.querySelectorAll(".auth-tab");
    const authForms = document.querySelectorAll(".auth-form");
    const loginForm = document.getElementById("login-form");
    const registerForm = document.getElementById("register-form");
    const activationSection = document.getElementById("activation-section");
    const userInfo = document.getElementById("user-info");
    const expiredInterface = document.getElementById("expired-interface");
    const message = document.getElementById("message");
    const loading = document.getElementById("loading");
    const purchaseLink = document.getElementById("purchase-link");

    // 登录表单元素
    const loginUsername = document.getElementById("login-username");
    const loginPassword = document.getElementById("login-password");
    const loginBtn = document.getElementById("login-btn");

    // 注册表单元素
    const registerUsername = document.getElementById("register-username");
    const registerPassword = document.getElementById("register-password");
    const registerConfirm = document.getElementById("register-confirm");
    const registerBtn = document.getElementById("register-btn");

    // 用户信息元素
    const userName = document.getElementById("user-name");
    const userStatus = document.getElementById("user-status");
    const gotoMainBtn = document.getElementById("goto-main-btn");
    const testJumpBtn = document.getElementById("test-jump-btn");
    const logoutBtn = document.getElementById("logout-btn");

    // 激活码元素
    const activationCode = document.getElementById("activation-code");
    const activateBtn = document.getElementById("activate-btn");
    const expiredActivationCode = document.getElementById("expired-activation-code");
    const expiredActivateBtn = document.getElementById("expired-activate-btn");

    let currentUser = null;

    // 切换标签页
    function switchTab(tabName) {
        authTabs.forEach(tab => {
            tab.classList.remove("active");
            if (tab.dataset.tab === tabName) {
                tab.classList.add("active");
            }
        });

        authForms.forEach(form => {
            form.classList.remove("active");
            if (form.id === `${tabName}-form`) {
                form.classList.add("active");
            }
        });
    }

    // 破解版登录处理
    async function handleLogin() {
        console.log("🔑 破解版登录处理");
        const username = loginUsername.value.trim();
        const password = loginPassword.value.trim();

        if (!username || !password) {
            showMessage("请填写用户名和密码", "error");
            return;
        }

        setLoading(true);

        try {
            // 使用破解版用户数据管理器
            const result = await window.userDataManager.login(username, password);

            if (result.success) {
                currentUser = result.userData;

                // 通知授权状态变化
                await notifyAuthStatusChange(true, currentUser);

                showMessage("登录成功！", "success");
                console.log("🎉 破解版登录成功");

                setTimeout(() => {
                    showUserInfo();
                }, 1000);
            } else {
                showMessage(result.message, "error");
            }
        } catch (error) {
            console.error("登录请求失败:", error);
            showMessage("登录失败", "error");
        } finally {
            setLoading(false);
        }
    }

    // 破解版注册处理
    async function handleRegister() {
        console.log("📝 破解版注册处理");
        const username = registerUsername.value.trim();
        const password = registerPassword.value.trim();
        const confirmPassword = registerConfirm.value.trim();

        if (!username || !password || !confirmPassword) {
            showMessage("请填写所有字段", "error");
            return;
        }

        if (password !== confirmPassword) {
            showMessage("两次输入的密码不一致", "error");
            return;
        }

        if (username.length < 3 || username.length > 20) {
            showMessage("用户名长度必须在3-20个字符之间", "error");
            return;
        }

        if (!/^[a-zA-Z0-9_]+$/.test(username)) {
            showMessage("用户名只能包含字母、数字和下划线", "error");
            return;
        }

        if (password.length < 6) {
            showMessage("密码长度至少6个字符", "error");
            return;
        }

        setLoading(true);

        try {
            const result = await window.userDataManager.register(username, password, "");

            if (result.success) {
                currentUser = result.userData;
                showMessage("注册成功！", "success");

                setTimeout(() => {
                    showUserInfo();
                }, 1500);
            } else {
                showMessage(result.message, "error");
            }
        } catch (error) {
            console.error("注册请求失败:", error);
            showMessage("注册失败", "error");
        } finally {
            setLoading(false);
        }
    }

    // 破解版激活处理
    async function handleActivation() {
        const code = activationCode.value.trim();

        if (!code) {
            showMessage("请输入激活码", "error");
            return;
        }

        if (!currentUser || !currentUser.user_id) {
            showMessage("请先登录", "error");
            return;
        }

        setLoading(true);

        try {
            const result = await window.userDataManager.activate(code);

            if (result.success) {
                currentUser = result.userData;
                await notifyAuthStatusChange(true, currentUser);
                showMessage("激活成功！", "success");
                activationCode.value = "";
                hideActivationSection();

                setTimeout(() => {
                    console.log("🚀 激活成功，跳转到主界面...");
                    redirectToMain();
                }, 2000);
            } else {
                showMessage(result.message, "error");
            }
        } catch (error) {
            console.error("激活失败:", error);
            showMessage("激活失败", "error");
        } finally {
            setLoading(false);
        }
    }

    // 过期用户激活处理
    async function handleExpiredActivation() {
        const code = expiredActivationCode.value.trim();

        if (!code) {
            showMessage("请输入激活码", "error");
            return;
        }

        const userData = await window.userDataManager.get();
        if (!userData || !userData.user_id) {
            showMessage("用户信息丢失，请重新登录", "error");
            return;
        }

        expiredActivateBtn.disabled = true;
        expiredActivateBtn.textContent = "激活中...";

        try {
            const result = await window.userDataManager.activate(code);

            if (result.success) {
                currentUser = result.userData;
                await notifyAuthStatusChange(true, currentUser);
                showMessage("激活成功！", "success");

                setTimeout(() => {
                    console.log("🚀 过期用户激活成功，跳转到主界面...");
                    redirectToMain();
                }, 1500);
            } else {
                showMessage(result.message, "error");
            }
        } catch (error) {
            console.error("激活失败:", error);
            showMessage("激活失败", "error");
        } finally {
            expiredActivateBtn.disabled = false;
            expiredActivateBtn.textContent = "✨ 激活";
        }
    }

    // 退出登录
    async function handleLogout() {
        await window.userDataManager.logout();
        await notifyAuthStatusChange(false);
        showMessage("已退出登录", "success");

        setTimeout(() => {
            location.reload();
        }, 1000);
    }

    // 显示过期用户界面
    async function showExpiredInterface(userData) {
        hideAllSections();
        expiredInterface.classList.add("show");

        if (userData) {
            currentUser = userData;
            await window.userDataManager.set(userData);

            if (window.currentPurchaseLink) {
                userData.purchase_link = window.currentPurchaseLink;
            }

            console.log("🚫 用户账户已过期，自动停用插件功能");
            await disablePluginForExpiredUser();
        }
    }

    // 隐藏所有区域
    function hideAllSections() {
        loginForm.classList.remove("active");
        registerForm.classList.remove("active");
        userInfo.classList.remove("show");
        activationSection.classList.remove("show");
        expiredInterface.classList.remove("show");
        document.querySelector(".auth-tabs").style.display = "none";
    }

    // 显示用户信息
    function showUserInfo() {
        hideAllSections();
        userInfo.classList.add("show");
        updateUserInfo();
    }

    // 更新用户信息显示
    function updateUserInfo() {
        if (currentUser) {
            userName.textContent = currentUser.username;
            userStatus.textContent = `剩余天数: ${currentUser.remaining_days} 天 | 状态: ${currentUser.status}`;
        }
    }

    // 隐藏激活区域
    function hideActivationSection() {
        activationSection.classList.remove("show");
    }

    // 设置购买链接
    function setPurchaseLink(link) {
        window.currentPurchaseLink = link;
        console.log("购买链接已更新:", link);
        if (purchaseLink && link && link !== "#") {
            purchaseLink.href = link;
        }
    }

    // 显示消息
    function showMessage(text, type) {
        message.textContent = text;
        message.className = `message ${type}`;
        message.style.display = "block";

        setTimeout(() => {
            message.style.display = "none";
        }, 3000);
    }

    // 设置加载状态
    function setLoading(isLoading) {
        if (isLoading) {
            loading.classList.add("show");
            loginBtn.disabled = true;
            registerBtn.disabled = true;
            activateBtn.disabled = true;
        } else {
            loading.classList.remove("show");
            loginBtn.disabled = false;
            registerBtn.disabled = false;
            activateBtn.disabled = false;
        }
    }

    // 通知授权状态变化
    async function notifyAuthStatusChange(authorized, userData = null) {
        try {
            await chrome.runtime.sendMessage({
                type: "AUTH_STATUS_CHANGE",
                authorized: authorized,
                userData: userData
            });
        } catch (error) {
            console.error("通知授权状态变化失败:", error);
        }
    }

    // 停用插件功能
    async function disablePluginForExpiredUser() {
        try {
            console.log("🚫 用户账户已过期，自动停用插件功能");

            try {
                await chrome.runtime.sendMessage({
                    type: "DISABLE_PLUGIN_FOR_EXPIRED_USER"
                });
            } catch (error) {
                console.log("无法通知background script，直接在当前页面停用");
            }

            localStorage.setItem("plugin_manually_activated", "false");
            localStorage.removeItem("plugin_activation_time");
            localStorage.removeItem("plugin_auth_status");
            localStorage.removeItem("plugin_user_info");

            console.log("✅ 插件功能已停用");
        } catch (error) {
            console.error("❌ 停用插件功能失败:", error);
        }
    }

    // 跳转到主界面
    async function redirectToMain() {
        console.log("🚀 认证成功，跳转到主界面...");
        window.location.href = "popup.html";
    }

    // 绑定事件监听器
    console.log("🔧 开始绑定事件监听器...");

    // 标签页切换
    if (authTabs.length > 0) {
        authTabs.forEach((tab, index) => {
            console.log(`绑定标签页 ${index}:`, tab.dataset.tab);
            tab.addEventListener("click", () => {
                console.log("标签页被点击:", tab.dataset.tab);
                switchTab(tab.dataset.tab);
            });
        });
        console.log("✅ 标签页事件已绑定");
    }

    // 登录按钮
    if (loginBtn) {
        loginBtn.addEventListener("click", (e) => {
            console.log("🔑 登录按钮被点击");
            e.preventDefault();
            handleLogin();
        });
        console.log("✅ 登录按钮事件已绑定");
    } else {
        console.error("❌ 登录按钮未找到");
    }

    // 注册按钮
    if (registerBtn) {
        registerBtn.addEventListener("click", (e) => {
            console.log("📝 注册按钮被点击");
            e.preventDefault();
            handleRegister();
        });
        console.log("✅ 注册按钮事件已绑定");
    } else {
        console.error("❌ 注册按钮未找到");
    }

    // 激活按钮
    activateBtn.addEventListener("click", handleActivation);
    expiredActivateBtn.addEventListener("click", handleExpiredActivation);

    // 主界面按钮
    if (gotoMainBtn) {
        gotoMainBtn.addEventListener("click", () => {
            console.log("🚀 主界面按钮被点击");
            redirectToMain();
        });
    }

    // 测试跳转按钮
    if (testJumpBtn) {
        testJumpBtn.addEventListener("click", () => {
            console.log("🧪 测试跳转按钮被点击");
            redirectToMain();
        });
    }

    // 退出登录按钮
    if (logoutBtn) {
        logoutBtn.addEventListener("click", handleLogout);
    }

    // 购买链接
    if (purchaseLink) {
        purchaseLink.addEventListener("click", (e) => {
            e.preventDefault();
            console.log("🛒 购买链接被点击");

            let linkUrl = null;
            if (currentUser && currentUser.purchase_link && currentUser.purchase_link !== "#") {
                linkUrl = currentUser.purchase_link;
            } else if (window.currentPurchaseLink && window.currentPurchaseLink !== "#") {
                linkUrl = window.currentPurchaseLink;
            } else {
                linkUrl = "https://xoxome.online/?page_id=1685";
            }

            chrome.tabs.create({url: linkUrl});
        });
    }

    // 回车键处理
    loginPassword.addEventListener("keypress", (e) => {
        if (e.key === "Enter") {
            handleLogin();
        }
    });

    registerConfirm.addEventListener("keypress", (e) => {
        if (e.key === "Enter") {
            handleRegister();
        }
    });

    activationCode.addEventListener("keypress", (e) => {
        if (e.key === "Enter") {
            handleActivation();
        }
    });

    expiredActivationCode.addEventListener("keypress", (e) => {
        if (e.key === "Enter") {
            handleExpiredActivation();
        }
    });

    // 初始化检查
    (async function initCheck() {
        if (await window.userDataManager.isLoggedIn()) {
            try {
                const userData = await window.userDataManager.validateWithServer();
                if (userData) {
                    currentUser = userData;
                    console.log("🎉 破解版本：自动登录成功");
                    window.location.href = "popup.html";
                } else {
                    await window.userDataManager.clear();
                }
            } catch (error) {
                console.error("验证失败:", error);
                await window.userDataManager.clear();
            }
        }
    })();
}

// 加载用户数据管理器
script.src = "user-data-manager.js";
document.head.appendChild(script);

document.addEventListener("DOMContentLoaded", function() {
    console.log("🚀 破解版认证页面已加载");
    setTimeout(initializeAuth, 100);
});