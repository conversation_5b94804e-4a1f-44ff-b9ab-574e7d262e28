const script=document.createElement("script");function initializeAuth(){console.log("🔐 初始化认证系统...");const e="http://*************/api";console.log("🌐 API基础URL:",e);const t=document.querySelectorAll(".auth-tab"),o=document.querySelectorAll(".auth-form"),n=document.getElementById("login-form"),a=document.getElementById("register-form"),s=document.getElementById("activation-section"),c=document.getElementById("user-info"),r=document.getElementById("expired-interface"),l=document.getElementById("message"),i=document.getElementById("loading"),d=document.getElementById("purchase-link"),u=document.getElementById("login-username"),g=document.getElementById("login-password"),m=document.getElementById("login-btn"),y=document.getElementById("register-username"),w=document.getElementById("register-password"),h=document.getElementById("register-confirm"),v=document.getElementById("register-btn"),f=document.getElementById("user-name"),p=document.getElementById("user-status"),E=document.getElementById("goto-main-btn"),_=document.getElementById("test-jump-btn"),L=document.getElementById("logout-btn"),I=document.getElementById("activation-code"),k=document.getElementById("activate-btn"),b=document.getElementById("expired-activation-code"),B=document.getElementById("expired-activate-btn");let D=null;function x(e){t.forEach(t=>{t.classList.remove("active"),t.dataset.tab===e&&t.classList.add("active")}),o.forEach(t=>{t.classList.remove("active"),t.id===`${e}-form`&&t.classList.add("active")})}async function M(){console.log(1107);const e=u.value.trim(),t=g.value.trim();if(console.log(1407),console.log(1408),e&&t){R(!0);try{const o=await window.userDataManager.login(e,t);console.log(1409),o.success?(D=o.userData,D.remaining_days>0&&"active"===D.status?await j(!0,D):await j(!1),O("登录成功！","success"),console.log(1108),setTimeout(()=>{D.remaining_days>0?(console.log(1109),q()):(console.log(1110),C(D))},1e3)):(console.log(1111),O(o.message,"error"),o.data&&o.data.expired&&o.data.user_data&&(z(o.data.purchase_link),await C(o.data.user_data)))}catch(e){console.error("登录请求失败:",e),O("登录失败，请检查网络连接","error")}finally{R(!1)}}else O("请填写用户名和密码","error")}async function P(){console.log("📝 开始处理注册...");const e=y.value.trim(),t=w.value.trim(),o=h.value.trim();if(console.log("注册用户名:",e),console.log("密码长度:",t.length),console.log("确认密码长度:",o.length),e&&t&&o)if(t===o)if(e.length<3||e.length>20)O("用户名长度必须在3-20个字符之间","error");else if(/^[a-zA-Z0-9_]+$/.test(e))if(t.length<6)O("密码长度至少6个字符","error");else{R(!0);try{const o=await window.userDataManager.register(e,t,"");console.log("📥 注册结果:",o),o.success?"expired"===o.userData.status||o.userData.remaining_days<=0?(O("注册成功！","success"),D=o.userData,setTimeout(()=>{C(o.userData)},1500)):(O("注册成功！请登录","success"),y.value="",w.value="",h.value="",setTimeout(()=>{x("login"),u.value=e},1500)):O(o.message,"error")}catch(e){console.error("注册请求失败:",e),O("注册失败，请检查网络连接","error")}finally{R(!1)}}else O("用户名只能包含字母、数字和下划线","error");else O("两次输入的密码不一致","error");else O("请填写所有字段","error")}async function A(){const e=I.value.trim();if(e)if(D&&D.user_id){R(!0);try{const t=await window.userDataManager.activate(e);t.success?(D=t.userData,D.remaining_days>0&&"active"===D.status?await j(!0,D):await j(!1),O("激活成功！","success"),I.value="",U(),D.remaining_days>0&&setTimeout(()=>{console.log("🚀 激活成功，跳转到主界面..."),q()},2e3)):O(t.message,"error")}catch(e){console.error("激活失败:",e),O("激活失败，请检查网络连接","error")}finally{R(!1)}}else O("请先登录","error");else O("请输入激活码","error")}async function S(){const e=b.value.trim();if(!e)return void O("请输入激活码","error");const t=await async function(){return await window.userDataManager.get()}();if(t&&t.user_id){B.disabled=!0,B.textContent="激活中...";try{const t=await window.userDataManager.activate(e);t.success?(D=t.userData,D.remaining_days>0&&"active"===D.status?await j(!0,D):await j(!1),O("激活成功！","success"),setTimeout(()=>{console.log("🚀 过期用户激活成功，跳转到主界面..."),q()},1500)):O(t.message,"error")}catch(e){console.error("激活失败:",e),O("激活失败，请检查网络连接","error")}finally{B.disabled=!1,B.textContent="✨ 激活"}}else O("用户信息丢失，请重新登录","error")}async function T(){await window.userDataManager.logout(),await j(!1),O("已退出登录","success"),setTimeout(()=>{location.reload()},1e3)}async function C(e){$(),r.classList.add("show"),e&&(D=e,await async function(e){return await window.userDataManager.set(e)}(e),window.currentPurchaseLink&&(e.purchase_link=window.currentPurchaseLink),console.log("🚫 用户账户已过期，自动停用插件功能"),await async function(){try{console.log("🚫 用户账户已过期，自动停用插件功能");try{await chrome.runtime.sendMessage({type:"DISABLE_PLUGIN_FOR_EXPIRED_USER"})}catch(e){console.log("无法通知background script，直接在当前页面停用")}localStorage.setItem("plugin_manually_activated","false"),localStorage.removeItem("plugin_activation_time"),localStorage.removeItem("plugin_auth_status"),localStorage.removeItem("plugin_user_info"),console.log("✅ 插件功能已停用")}catch(e){console.error("❌ 停用插件功能失败:",e)}}())}function $(){n.classList.remove("active"),a.classList.remove("active"),c.classList.remove("show"),s.classList.remove("show"),r.classList.remove("show"),document.querySelector(".auth-tabs").style.display="none"}function U(){D&&(f.textContent=D.username,p.textContent=`剩余天数: ${D.remaining_days} 天 | 状态: ${D.status}`)}function z(e){e&&d&&(d.href=e)}function O(e,type){l.textContent=e,l.className=`message ${type}`,l.style.display="block",setTimeout(()=>{l.style.display="none"},3e3)}function R(e){e?(i.classList.add("show"),m.disabled=!0,v.disabled=!0,k.disabled=!0):(i.classList.remove("show"),m.disabled=!1,v.disabled=!1,k.disabled=!1)}async function j(e,t=null){try{await chrome.runtime.sendMessage({type:"AUTH_STATUS_CHANGE",authorized:e,userData:t})}catch(e){console.error("通知授权状态变化失败:",e)}}function z(e){window.currentPurchaseLink=e,console.log("购买链接已更新:",e),d&&e&&"#"!==e&&(d.href=e)}async function q(){console.log("🚀 认证成功，跳转到主界面..."),window.location.href="popup.html"}console.log("🔍 验证DOM元素:"),console.log("- 登录表单:",n?"✅":"❌"),console.log("- 注册表单:",a?"✅":"❌"),console.log("- 登录按钮:",m?"✅":"❌"),console.log("- 注册按钮:",v?"✅":"❌"),console.log("- 登录用户名输入:",u?"✅":"❌"),console.log("- 登录密码输入:",g?"✅":"❌"),console.log("🔧 开始绑定事件监听器..."),console.log("DOM元素检查:"),console.log("- authTabs数量:",t.length),console.log("- loginBtn:",m),console.log("- registerBtn:",v),console.log("- loginUsername:",u),console.log("- loginPassword:",g),t.length>0&&(t.forEach((e,t)=>{console.log(`绑定标签页 ${t}:`,e.dataset.tab),e.addEventListener("click",()=>{console.log("标签页被点击:",e.dataset.tab),x(e.dataset.tab)})}),console.log("✅ 标签页事件已绑定")),m?(m.addEventListener("click",e=>{console.log("🔑 登录按钮被点击"),e.preventDefault(),M()}),console.log("✅ 登录按钮事件已绑定")):console.error("❌ 登录按钮未找到"),v?(v.addEventListener("click",e=>{console.log("📝 注册按钮被点击"),e.preventDefault(),P()}),console.log("✅ 注册按钮事件已绑定")):console.error("❌ 注册按钮未找到"),k.addEventListener("click",A),B.addEventListener("click",S),E&&E.addEventListener("click",()=>{console.log("🚀 主界面按钮被点击"),q()}),_&&_.addEventListener("click",()=>{console.log("🧪 测试跳转按钮被点击"),q()}),L&&L.addEventListener("click",T),d&&d.addEventListener("click",t=>{t.preventDefault(),console.log("🛒 购买链接被点击"),console.log("当前用户:",D),console.log("全局购买链接:",window.currentPurchaseLink);let o=null;if(D&&D.purchase_link&&"#"!==D.purchase_link)o=D.purchase_link,console.log("使用用户购买链接:",o);else{if(!window.currentPurchaseLink||"#"===window.currentPurchaseLink)return void async function(){console.log("🛒 尝试从API获取购买链接");try{const t=await fetch(`${e}/get_config.php?key=purchase_link`),o=await t.json();let n="https://xoxome.online/?page_id=1685";o.success&&o.data&&o.data.config_value?(n=o.data.config_value,console.log("从API获取到购买链接:",n)):console.log("API未返回购买链接，使用默认链接"),chrome.tabs.create({url:n})}catch(e){console.error("获取购买链接失败:",e),chrome.tabs.create({url:"https://xoxome.online/?page_id=1685"})}}();o=window.currentPurchaseLink,console.log("使用全局购买链接:",o)}o?chrome.tabs.create({url:o}):(console.log("没有购买链接，使用默认链接"),chrome.tabs.create({url:"https://xoxome.online/?page_id=1685"}))}),g.addEventListener("keypress",e=>{"Enter"===e.key&&M()}),h.addEventListener("keypress",e=>{"Enter"===e.key&&P()}),I.addEventListener("keypress",e=>{"Enter"===e.key&&A()}),b.addEventListener("keypress",e=>{"Enter"===e.key&&S()}),async function(){if(await window.userDataManager.isLoggedIn())try{const e=await window.userDataManager.validateWithServer();e?(D=e,e.remaining_days>0&&"active"===e.status?(console.log(1106),window.location.href="popup.html"):C(e)):await window.userDataManager.clear()}catch(e){console.error(1504),await window.userDataManager.clear()}}()}script.src="user-data-manager.js",document.head.appendChild(script),document.addEventListener("DOMContentLoaded",function(){console.log("🚀 认证页面已加载"),setTimeout(initializeAuth,100)});