<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FengCheTimezone 破解测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 28px;
        }
        
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .test-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #4CAF50;
        }
        
        .test-item {
            margin-bottom: 10px;
            padding: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 5px;
            font-family: monospace;
        }
        
        .test-label {
            font-weight: 600;
            color: #FFD700;
        }
        
        .test-value {
            color: #87CEEB;
        }
        
        .status-ok {
            color: #4CAF50;
        }
        
        .status-error {
            color: #f44336;
        }
        
        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            background: #45a049;
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background: #2196F3;
        }
        
        .btn-secondary:hover {
            background: #1976D2;
        }
        
        .instructions {
            background: rgba(255, 193, 7, 0.2);
            border: 1px solid rgba(255, 193, 7, 0.3);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .instructions h3 {
            margin-top: 0;
            color: #FFC107;
        }
        
        .instructions ol {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .instructions li {
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌍 FengCheTimezone 破解测试</h1>
        
        <div class="instructions">
            <h3>📋 测试说明</h3>
            <ol>
                <li>确保已安装破解版本的插件</li>
                <li>点击下方按钮进行各项功能测试</li>
                <li>检查时区、地理位置等信息是否被正确伪装</li>
                <li>如果测试失败，请检查插件是否正确安装</li>
            </ol>
        </div>
        
        <div class="test-section">
            <div class="test-title">🕐 时区信息测试</div>
            <div class="test-item">
                <span class="test-label">当前时区:</span>
                <span class="test-value" id="timezone">检测中...</span>
            </div>
            <div class="test-item">
                <span class="test-label">时区偏移:</span>
                <span class="test-value" id="timezone-offset">检测中...</span>
            </div>
            <div class="test-item">
                <span class="test-label">本地时间:</span>
                <span class="test-value" id="local-time">检测中...</span>
            </div>
            <button class="btn" onclick="testTimezone()">🔄 刷新时区信息</button>
        </div>
        
        <div class="test-section">
            <div class="test-title">📍 地理位置测试</div>
            <div class="test-item">
                <span class="test-label">纬度:</span>
                <span class="test-value" id="latitude">未获取</span>
            </div>
            <div class="test-item">
                <span class="test-label">经度:</span>
                <span class="test-value" id="longitude">未获取</span>
            </div>
            <div class="test-item">
                <span class="test-label">精度:</span>
                <span class="test-value" id="accuracy">未获取</span>
            </div>
            <div class="test-item">
                <span class="test-label">状态:</span>
                <span class="test-value" id="geo-status">未测试</span>
            </div>
            <button class="btn" onclick="testGeolocation()">📍 获取地理位置</button>
        </div>
        
        <div class="test-section">
            <div class="test-title">🌐 语言环境测试</div>
            <div class="test-item">
                <span class="test-label">主要语言:</span>
                <span class="test-value" id="language">检测中...</span>
            </div>
            <div class="test-item">
                <span class="test-label">语言列表:</span>
                <span class="test-value" id="languages">检测中...</span>
            </div>
            <button class="btn" onclick="testLanguage()">🔄 刷新语言信息</button>
        </div>
        
        <div class="test-section">
            <div class="test-title">💻 设备信息测试</div>
            <div class="test-item">
                <span class="test-label">设备内存:</span>
                <span class="test-value" id="device-memory">检测中...</span>
            </div>
            <div class="test-item">
                <span class="test-label">硬件并发:</span>
                <span class="test-value" id="hardware-concurrency">检测中...</span>
            </div>
            <div class="test-item">
                <span class="test-label">用户代理:</span>
                <span class="test-value" id="user-agent" style="font-size: 12px;">检测中...</span>
            </div>
            <button class="btn" onclick="testDeviceInfo()">🔄 刷新设备信息</button>
        </div>
        
        <div class="test-section">
            <div class="test-title">🎮 WebGL 信息测试</div>
            <div class="test-item">
                <span class="test-label">WebGL 供应商:</span>
                <span class="test-value" id="webgl-vendor">检测中...</span>
            </div>
            <div class="test-item">
                <span class="test-label">WebGL 渲染器:</span>
                <span class="test-value" id="webgl-renderer">检测中...</span>
            </div>
            <button class="btn" onclick="testWebGL()">🔄 刷新WebGL信息</button>
        </div>
        
        <div class="test-section">
            <div class="test-title">🔧 综合测试</div>
            <button class="btn btn-secondary" onclick="runAllTests()">🚀 运行所有测试</button>
            <button class="btn btn-secondary" onclick="exportResults()">📄 导出测试结果</button>
        </div>
    </div>

    <script>
        // 时区测试
        function testTimezone() {
            try {
                const now = new Date();
                const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
                const offset = now.getTimezoneOffset();
                const localTime = now.toLocaleString();
                
                document.getElementById('timezone').textContent = timezone;
                document.getElementById('timezone-offset').textContent = `${offset} 分钟`;
                document.getElementById('local-time').textContent = localTime;
            } catch (error) {
                console.error('时区测试失败:', error);
                document.getElementById('timezone').textContent = '测试失败';
            }
        }
        
        // 地理位置测试
        function testGeolocation() {
            if (!navigator.geolocation) {
                document.getElementById('geo-status').textContent = '不支持地理位置API';
                return;
            }
            
            document.getElementById('geo-status').textContent = '获取中...';
            
            navigator.geolocation.getCurrentPosition(
                function(position) {
                    document.getElementById('latitude').textContent = position.coords.latitude.toFixed(6);
                    document.getElementById('longitude').textContent = position.coords.longitude.toFixed(6);
                    document.getElementById('accuracy').textContent = position.coords.accuracy + ' 米';
                    document.getElementById('geo-status').textContent = '获取成功';
                },
                function(error) {
                    document.getElementById('geo-status').textContent = '获取失败: ' + error.message;
                },
                {
                    enableHighAccuracy: true,
                    timeout: 10000,
                    maximumAge: 0
                }
            );
        }
        
        // 语言环境测试
        function testLanguage() {
            try {
                document.getElementById('language').textContent = navigator.language;
                document.getElementById('languages').textContent = navigator.languages.join(', ');
            } catch (error) {
                console.error('语言测试失败:', error);
                document.getElementById('language').textContent = '测试失败';
            }
        }
        
        // 设备信息测试
        function testDeviceInfo() {
            try {
                document.getElementById('device-memory').textContent = 
                    navigator.deviceMemory ? navigator.deviceMemory + ' GB' : '不支持';
                document.getElementById('hardware-concurrency').textContent = 
                    navigator.hardwareConcurrency || '未知';
                document.getElementById('user-agent').textContent = navigator.userAgent;
            } catch (error) {
                console.error('设备信息测试失败:', error);
            }
        }
        
        // WebGL测试
        function testWebGL() {
            try {
                const canvas = document.createElement('canvas');
                const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
                
                if (gl) {
                    const vendor = gl.getParameter(gl.VENDOR);
                    const renderer = gl.getParameter(gl.RENDERER);
                    
                    document.getElementById('webgl-vendor').textContent = vendor;
                    document.getElementById('webgl-renderer').textContent = renderer;
                } else {
                    document.getElementById('webgl-vendor').textContent = '不支持WebGL';
                    document.getElementById('webgl-renderer').textContent = '不支持WebGL';
                }
            } catch (error) {
                console.error('WebGL测试失败:', error);
                document.getElementById('webgl-vendor').textContent = '测试失败';
                document.getElementById('webgl-renderer').textContent = '测试失败';
            }
        }
        
        // 运行所有测试
        function runAllTests() {
            console.log('🚀 开始运行所有测试...');
            testTimezone();
            testGeolocation();
            testLanguage();
            testDeviceInfo();
            testWebGL();
            console.log('✅ 所有测试完成');
        }
        
        // 导出测试结果
        function exportResults() {
            const results = {
                timestamp: new Date().toISOString(),
                timezone: document.getElementById('timezone').textContent,
                timezoneOffset: document.getElementById('timezone-offset').textContent,
                localTime: document.getElementById('local-time').textContent,
                latitude: document.getElementById('latitude').textContent,
                longitude: document.getElementById('longitude').textContent,
                accuracy: document.getElementById('accuracy').textContent,
                geoStatus: document.getElementById('geo-status').textContent,
                language: document.getElementById('language').textContent,
                languages: document.getElementById('languages').textContent,
                deviceMemory: document.getElementById('device-memory').textContent,
                hardwareConcurrency: document.getElementById('hardware-concurrency').textContent,
                userAgent: document.getElementById('user-agent').textContent,
                webglVendor: document.getElementById('webgl-vendor').textContent,
                webglRenderer: document.getElementById('webgl-renderer').textContent
            };
            
            const blob = new Blob([JSON.stringify(results, null, 2)], {type: 'application/json'});
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'fengche-timezone-test-results.json';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            console.log('📄 测试结果已导出:', results);
        }
        
        // 页面加载时自动运行测试
        window.addEventListener('load', function() {
            console.log('🌍 FengCheTimezone 破解测试页面已加载');
            setTimeout(runAllTests, 1000);
        });
    </script>
</body>
</html>
