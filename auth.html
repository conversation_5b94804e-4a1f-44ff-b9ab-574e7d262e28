<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FengCheTimezone</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            width: 380px;
            min-height: 500px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            overflow-x: hidden;
        }

        .header {
            padding: 20px;
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .header h1 {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .header .subtitle {
            font-size: 12px;
            opacity: 0.8;
        }

        .content {
            padding: 20px;
        }

        .auth-container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .auth-tabs {
            display: flex;
            margin-bottom: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 4px;
        }

        .auth-tab {
            flex: 1;
            padding: 10px;
            text-align: center;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            font-weight: 500;
        }

        .auth-tab.active {
            background: rgba(255, 255, 255, 0.2);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .auth-form {
            display: none;
        }

        .auth-form.active {
            display: block;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-size: 13px;
            font-weight: 500;
            opacity: 0.9;
        }

        .form-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-group input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .form-group input:focus {
            outline: none;
            border-color: #4CAF50;
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
        }

        .btn {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 10px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }

        .btn-primary:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .message {
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 15px;
            font-size: 13px;
            text-align: center;
            display: none;
        }

        .message.success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.3);
            color: #4CAF50;
        }

        .message.error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid rgba(244, 67, 54, 0.3);
            color: #f44336;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 10px;
            font-size: 13px;
            opacity: 0.8;
        }

        .loading.show {
            display: block;
        }

        .user-info {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .user-info.show {
            display: block;
        }

        .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #4CAF50, #45a049);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 24px;
        }

        .user-details {
            margin-bottom: 20px;
        }

        .user-name {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .user-status {
            font-size: 13px;
            opacity: 0.8;
        }

        .activation-section {
            display: none;
            margin-top: 20px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .activation-section.show {
            display: block;
        }

        .activation-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 10px;
            text-align: center;
        }

        .expired-interface {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .expired-interface.show {
            display: block;
        }

        .expired-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }

        .expired-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #ff9800;
        }

        .expired-message {
            font-size: 13px;
            opacity: 0.8;
            margin-bottom: 20px;
            line-height: 1.4;
        }

        .purchase-link {
            display: inline-block;
            padding: 10px 20px;
            background: linear-gradient(135deg, #ff9800, #f57c00);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-size: 13px;
            font-weight: 600;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }

        .purchase-link:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3);
        }

        .footer {
            text-align: center;
            padding: 15px;
            font-size: 11px;
            opacity: 0.6;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🌍 FengCheTimezone</h1>
        <div class="subtitle">用户认证系统</div>
    </div>

    <div class="content">
        <div class="auth-container" id="auth-container">
            <div id="message" class="message"></div>
            <div id="loading" class="loading">⏳ 处理中...</div>

            <!-- 认证标签页 -->
            <div class="auth-tabs">
                <div class="auth-tab active" data-tab="login">登录</div>
                <div class="auth-tab" data-tab="register">注册</div>
            </div>

            <!-- 登录表单 -->
            <div class="auth-form active" id="login-form">
                <div class="form-group">
                    <label for="login-username">用户名</label>
                    <input type="text" id="login-username" placeholder="请输入用户名" maxlength="20">
                </div>
                <div class="form-group">
                    <label for="login-password">密码</label>
                    <input type="password" id="login-password" placeholder="请输入密码">
                </div>
                <button class="btn btn-primary" id="login-btn">🔑 登录</button>
            </div>

            <!-- 注册表单 -->
            <div class="auth-form" id="register-form">
                <div class="form-group">
                    <label for="register-username">用户名</label>
                    <input type="text" id="register-username" placeholder="3-20个字符，仅限字母数字下划线" maxlength="20">
                </div>
                <div class="form-group">
                    <label for="register-password">密码</label>
                    <input type="password" id="register-password" placeholder="至少6个字符">
                </div>
                <div class="form-group">
                    <label for="register-confirm">确认密码</label>
                    <input type="password" id="register-confirm" placeholder="请再次输入密码">
                </div>
                <button class="btn btn-primary" id="register-btn">📝 注册</button>
            </div>

            <!-- 用户信息界面 -->
            <div class="user-info" id="user-info">
                <div class="user-avatar">👤</div>
                <div class="user-details">
                    <div class="user-name" id="user-name">用户名</div>
                    <div class="user-status" id="user-status">状态信息</div>
                </div>
                <button class="btn btn-primary" id="goto-main-btn">🚀 进入主界面</button>
                <button class="btn btn-primary" id="test-jump-btn" style="background: #2196F3; margin-top: 10px;">🧪 测试跳转</button>
                <button class="btn btn-primary" id="logout-btn" style="background: #f44336; margin-top: 10px;">🚪 退出登录</button>
            </div>

            <!-- 激活码输入区域 -->
            <div class="activation-section" id="activation-section">
                <div class="activation-title">🔑 输入激活码</div>
                <div class="form-group">
                    <input type="text" id="activation-code" placeholder="请输入激活码" maxlength="50">
                </div>
                <button class="btn btn-primary" id="activate-btn">✨ 激活</button>
            </div>

            <!-- 过期用户界面 -->
            <div class="expired-interface" id="expired-interface">
                <div class="expired-icon">⏰</div>
                <div class="expired-title">账户已过期</div>
                <div class="expired-message">
                    您的账户使用期限已到期。<br>
                    请购买激活码或联系客服续费。
                </div>
                <a href="#" class="purchase-link" id="purchase-link" target="_blank">💳 购买激活码</a>
                <div class="activation-section show">
                    <div class="activation-title">🔑 输入激活码续费</div>
                    <div class="form-group">
                        <input type="text" id="expired-activation-code" placeholder="请输入激活码" maxlength="50">
                    </div>
                    <button class="btn btn-primary" id="expired-activate-btn">✨ 激活</button>
                </div>
            </div>
        </div>
    </div>

    <div class="footer">
        <div>FengCheTimezone by xfish</div>
    </div>

    <script src="auth.js"></script>
</body>
</html>
