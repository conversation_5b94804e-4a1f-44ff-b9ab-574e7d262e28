class UserDataManager{constructor(){this.STORAGE_KEY="plugin_user_data",this.API_BASE="http://103.96.75.196/api"}async get(){try{const e=await chrome.storage.local.get([this.STORAGE_KEY]);if(e[this.STORAGE_KEY])return e[this.STORAGE_KEY];return await this.migrateOldData()}catch(e){return console.error(1504),null}}async migrateOldData(){try{console.log(1800);const e=await chrome.storage.local.get(null);let a=null;return e.user_data&&e.user_data.username?(console.log(1801),a={user_id:e.user_data.user_id,username:e.user_data.username,remaining_days:e.user_data.remaining_days,status:e.user_data.status,login_time:e.login_time||Date.now(),last_validated:Date.now(),last_updated:Date.now()}):e.auth_user_info&&e.auth_user_info.username&&(console.log(1802),a={user_id:e.auth_user_info.user_id,username:e.auth_user_info.username,remaining_days:e.auth_user_info.remaining_days,status:e.auth_user_info.status,login_time:e.auth_updated_at||Date.now(),last_validated:Date.now(),last_updated:Date.now()}),a?(await this.set(a),await chrome.storage.local.remove(["user_data","login_time","auth_user_info","auth_updated_at"]),console.log(1803),a):(console.log(1804),null)}catch(e){return console.error(1504),null}}async set(e){try{const a={...e,last_updated:Date.now()};return await chrome.storage.local.set({[this.STORAGE_KEY]:a}),console.log(1810),!0}catch(e){return console.error(1504),!1}}async clear(){try{return await chrome.storage.local.remove([this.STORAGE_KEY]),console.log(1251),!0}catch(e){return console.error(1504),!1}}async isLoggedIn(){const e=await this.get();return!!(e&&e.user_id&&e.username)}async isExpired(){console.log(1820);const e=await this.get();if(!e)return console.log(1821),!0;if(console.log(1822),console.log(1823),e.expires_at){console.log(1824);const a=this.calculatePreciseTimeInfo(e.expires_at);return console.log(1825),console.log(1826),a.isExpired}console.log(1827);const a=e.remaining_days<=0||"expired"===e.status;return console.log(1828),console.log(1829),console.log(1830),console.log(1826),a}calculatePreciseTimeInfo(e){if(!e)return{isExpired:!0,remainingMs:0,remainingMinutes:0,remainingHours:0,remainingDays:0,expireTime:null,currentTime:null};try{const now=new Date,a=new Date(e);console.log(1840),console.log(1841),console.log(1842);const t=a.getTime()-now.getTime();console.log(1843);if(t<=0)return console.log(1844),{isExpired:!0,remainingMs:0,remainingMinutes:0,remainingHours:0,remainingDays:0,expireTime:a,currentTime:now};const s=t,r=Math.floor(t/1e3),n=Math.floor(t/6e4),o=Math.floor(t/36e5),i=Math.ceil(t/864e5);return console.log(`   剩余: ${i}天 ${o%24}小时 ${n%60}分钟 ${r%60}秒`),{isExpired:!1,remainingMs:s,remainingSeconds:r,remainingMinutes:n,remainingHours:o,remainingDays:Math.max(0,i),expireTime:a,currentTime:now}}catch(e){return console.error("❌ 精确时间计算失败:",e),{isExpired:!0,remainingMs:0,remainingMinutes:0,remainingHours:0,remainingDays:0,expireTime:null,currentTime:null,error:e.message}}}calculateStandardRemainingDays(e){return this.calculatePreciseTimeInfo(e).remainingDays}calculateRealTimeExpiration(e){if(console.log("🔧 calculateRealTimeExpiration 调用:"),console.log("   输入userData:",e),!e)return console.log("   ❌ 没有用户数据，返回过期状态"),{remainingDays:0,isExpired:!0,expireDate:null,expireTimestamp:null};if(e.expires_at){console.log(`   ✅ 找到过期时间: ${e.expires_at}`);try{const a=this.calculatePreciseTimeInfo(e.expires_at);console.log("   📊 精确计算结果:",a);const t={remainingDays:a.remainingDays,remainingHours:a.remainingHours,remainingMinutes:a.remainingMinutes,remainingSeconds:a.remainingSeconds,isExpired:a.isExpired,expireDate:a.expireTime?a.expireTime.toLocaleString():null,expireTimestamp:a.expireTime?a.expireTime.getTime():null,expireDateISO:e.expires_at,calculationMethod:"precise_server_expires_at",timeInfo:a};return console.log("   🎯 返回结果:",t),t}catch(e){console.error("❌ 过期时间计算失败:",e)}}else console.log("   ⚠️ 没有expires_at字段，使用降级逻辑");if(e.last_validated&&e.remaining_days>0)try{const a=new Date(e.last_validated).getTime()+24*e.remaining_days*60*60*1e3,t=new Date(a),now=new Date,s=a-now.getTime(),r=Math.ceil(s/864e5);return{remainingDays:Math.max(0,r),isExpired:now.getTime()>=a,expireDate:t.toLocaleString(),expireTimestamp:a,expireDateISO:t.toISOString(),calculationMethod:"last_validated_based"}}catch(e){console.error("❌ 基于验证时间的计算失败:",e)}console.log(1827),console.log(1828),console.log(1829);const a={remainingDays:e.remaining_days||0,isExpired:e.remaining_days<=0||"expired"===e.status,expireDate:null,expireTimestamp:null,calculationMethod:"fallback_original_logic"};return console.log(1850),a}async getStatus(){const e=await this.get();if(!e)return{loggedIn:!1,expired:!0,remainingDays:0,status:"not_logged_in"};const a=this.calculateRealTimeExpiration(e);return{loggedIn:!0,expired:await this.isExpired(),remainingDays:a.remainingDays,status:e.status||"unknown",username:e.username,userId:e.user_id,expireDate:a.expireDate,expireTimestamp:a.expireTimestamp,expireDateISO:a.expireDateISO}}async validateWithServer(){try{const e=await this.get();if(!e||!e.user_id)return console.log("🔒 没有本地用户数据，无法验证"),null;console.log("🔍 正在验证用户状态...");const a=await fetch(`${this.API_BASE}/user_status.php?user_id=${e.user_id}`),t=await a.json();if(t.success&&t.data){const a={user_id:e.user_id,username:t.data.username,remaining_days:t.data.remaining_days,status:t.data.status,expires_at:t.data.expires_at,login_time:e.login_time||Date.now(),last_validated:Date.now()};return await this.set(a),console.log("✅ 用户状态验证成功:",t.data.username,"剩余天数:",t.data.remaining_days),a}return console.log("❌ 服务器验证失败:",t.message),null}catch(e){return console.error("❌ 服务器验证出错:",e),await this.get()}}async login(e,a){try{console.log("🔐 正在登录...");const t=await fetch(`${this.API_BASE}/login.php`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({username:e,password:a})}),s=await t.json();if(s.success&&s.data){const e={user_id:s.data.user_id,username:s.data.username,remaining_days:s.data.remaining_days,status:s.data.status,expires_at:s.data.expires_at,login_time:Date.now(),last_validated:Date.now()};return await this.set(e),console.log(1860),{success:!0,userData:e,message:"登录成功"}}return console.log(1111),{success:!1,message:s.message||"登录失败",data:s.data||null}}catch(e){return console.error(1504),{success:!1,message:"网络连接失败，请检查网络设置"}}}async checkDuplicateRegistration(){try{console.log(1870);const e=await Promise.all([new Promise(e=>{try{e(!!localStorage.getItem("plugin_reg_mark"))}catch(a){e(!1)}}),new Promise(e=>{chrome.storage.local.get(["plugin_reg_mark"],a=>{e(!!a.plugin_reg_mark)})}),new Promise(e=>{try{chrome.storage.sync.get(["plugin_reg_mark"],a=>{e(!!a.plugin_reg_mark)})}catch(a){e(!1)}}),new Promise(e=>{try{const a=indexedDB.open("PluginRegDB",1);a.onsuccess=a=>{try{const t=a.target.result;if(t.objectStoreNames.contains("marks")){const a=t.transaction(["marks"],"readonly"),s=a.objectStore("marks").get("reg_mark");s.onsuccess=()=>{e(!!s.result)},s.onerror=()=>e(!1)}else e(!1)}catch(a){e(!1)}},a.onerror=()=>e(!1),a.onblocked=()=>e(!1)}catch(a){e(!1)}})]);return e.some(e=>!0===e)?(console.log(1871),!0):(console.log(1872),!1)}catch(e){return console.error(1504),!1}}async createRegistrationMark(e){try{console.log(1880);const a={username:e,timestamp:Date.now(),browser_id:this.generateBrowserId(),version:"5.3.1"},t=[new Promise(e=>{try{localStorage.setItem("plugin_reg_mark",JSON.stringify(a)),localStorage.setItem("plugin_reg_time",Date.now().toString()),e(!0)}catch(a){e(!1)}}),new Promise(e=>{chrome.storage.local.set({plugin_reg_mark:a,plugin_reg_time:Date.now()},()=>{e(!0)})}),new Promise(e=>{try{chrome.storage.sync.set({plugin_reg_mark:a},()=>{e(!0)})}catch(a){e(!1)}}),new Promise(e=>{try{const t=indexedDB.open("PluginRegDB",1);t.onupgradeneeded=a=>{try{const e=a.target.result;e.objectStoreNames.contains("marks")||e.createObjectStore("marks")}catch(a){e(!1)}},t.onsuccess=t=>{try{const s=t.target.result.transaction(["marks"],"readwrite");s.objectStore("marks").put(a,"reg_mark"),s.oncomplete=()=>e(!0),s.onerror=()=>e(!1)}catch(a){e(!1)}},t.onerror=()=>e(!1),t.onblocked=()=>e(!1)}catch(a){e(!1)}})],s=await Promise.all(t),r=s.filter(e=>!0===e).length;console.log(`✅ 注册标记创建完成，成功 ${r}/${s.length} 个位置`)}catch(e){console.error("❌ 创建注册标记失败:",e)}}generateBrowserId(){try{const e=[navigator.userAgent,navigator.language,screen.width+"x"+screen.height,(new Date).getTimezoneOffset(),navigator.hardwareConcurrency||"unknown",navigator.deviceMemory||"unknown"].join("|");let a=0;for(let t=0;t<e.length;t++){a=(a<<5)-a+e.charCodeAt(t),a&=a}return Math.abs(a).toString(36)}catch(e){return"unknown_"+Date.now()}}async register(e,a,t){try{console.log(1890);if(await this.checkDuplicateRegistration())return console.log(1891),{success:!1,message:"检测到您已经注册过账户，每个用户只能注册一次。如需帮助请联系客服。",error_code:"DUPLICATE_REGISTRATION"};const s=await fetch(`${this.API_BASE}/register.php`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({username:e,password:a,activation_code:t})}),r=await s.json();if(r.success&&r.data){await this.createRegistrationMark(e);const a={user_id:r.data.user_id,username:r.data.username,remaining_days:r.data.remaining_days,status:r.data.status,expires_at:r.data.expires_at,login_time:Date.now(),last_validated:Date.now()};return await this.set(a),console.log(1892),{success:!0,userData:a,message:"注册成功"}}return console.log(1111),{success:!1,message:r.message||"注册失败"}}catch(e){return console.error("❌ 注册请求失败:",e),{success:!1,message:"网络连接失败，请检查网络设置"}}}async activate(e){try{const a=await this.get();if(!a||!a.user_id)return{success:!1,message:"请先登录"};console.log("⚡ 正在激活账户...");const t=await fetch(`${this.API_BASE}/activate.php`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({user_id:a.user_id,activation_code:e})}),s=await t.json();if(s.success&&s.data){const e={...a,remaining_days:s.data.remaining_days,status:s.data.status,expires_at:s.data.expires_at,last_validated:Date.now()};return await this.set(e),console.log("✅ 激活成功，剩余天数:",s.data.remaining_days),{success:!0,userData:e,message:"激活成功"}}return console.log("❌ 激活失败:",s.message),{success:!1,message:s.message||"激活失败"}}catch(e){return console.error("❌ 激活请求失败:",e),{success:!1,message:"网络连接失败，请检查网络设置"}}}async logout(){try{return console.log(1900),await this.clear(),!0}catch(e){return console.error(1504),!1}}}const userDataManager=new UserDataManager;"undefined"!=typeof window&&(window.userDataManager=userDataManager);