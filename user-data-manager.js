class UserDataManager {
    constructor() {
        this.STORAGE_KEY = "plugin_user_data";
        this.API_BASE = "http://103.96.75.196/api";
    }

    async get() {
        try {
            const result = await chrome.storage.local.get([this.STORAGE_KEY]);
            if (result[this.STORAGE_KEY]) {
                return result[this.STORAGE_KEY];
            }
            return await this.migrateOldData();
        } catch (error) {
            console.error("获取用户数据失败:", error);
            return null;
        }
    }

    async migrateOldData() {
        try {
            console.log("迁移旧数据...");
            const allData = await chrome.storage.local.get(null);
            let userData = null;

            if (allData.user_data && allData.user_data.username) {
                console.log("发现旧用户数据");
                userData = {
                    user_id: allData.user_data.user_id,
                    username: allData.user_data.username,
                    remaining_days: allData.user_data.remaining_days,
                    status: allData.user_data.status,
                    login_time: allData.login_time || Date.now(),
                    last_validated: Date.now(),
                    last_updated: Date.now()
                };
            } else if (allData.auth_user_info && allData.auth_user_info.username) {
                console.log("发现认证用户信息");
                userData = {
                    user_id: allData.auth_user_info.user_id,
                    username: allData.auth_user_info.username,
                    remaining_days: allData.auth_user_info.remaining_days,
                    status: allData.auth_user_info.status,
                    login_time: allData.auth_updated_at || Date.now(),
                    last_validated: Date.now(),
                    last_updated: Date.now()
                };
            }

            if (userData) {
                await this.set(userData);
                await chrome.storage.local.remove(["user_data", "login_time", "auth_user_info", "auth_updated_at"]);
                console.log("数据迁移完成");
                return userData;
            } else {
                console.log("没有找到可迁移的数据");
                return null;
            }
        } catch (error) {
            console.error("迁移数据失败:", error);
            return null;
        }
    }

    async set(userData) {
        try {
            const dataToStore = {
                ...userData,
                last_updated: Date.now()
            };
            await chrome.storage.local.set({[this.STORAGE_KEY]: dataToStore});
            console.log("用户数据已保存");
            return true;
        } catch (error) {
            console.error("保存用户数据失败:", error);
            return false;
        }
    }

    async clear() {
        try {
            await chrome.storage.local.remove([this.STORAGE_KEY]);
            console.log("用户数据已清除");
            return true;
        } catch (error) {
            console.error("清除用户数据失败:", error);
            return false;
        }
    }

    async isLoggedIn() {
        const userData = await this.get();
        return !!(userData && userData.user_id && userData.username);
    }

    // 破解：永远返回未过期
    async isExpired() {
        console.log("检查过期状态 - 破解版本：永远未过期");
        return false;
    }

    calculatePreciseTimeInfo(expiresAt) {
        if (!expiresAt) {
            return {
                isExpired: false,
                remainingMs: 999999999999,
                remainingMinutes: 999999999,
                remainingHours: 999999999,
                remainingDays: 999999,
                expireTime: null,
                currentTime: null
            };
        }

        try {
            const now = new Date();
            const expireTime = new Date(expiresAt);
            const timeDiff = expireTime.getTime() - now.getTime();

            if (timeDiff <= 0) {
                // 破解：即使过期也返回未过期状态
                return {
                    isExpired: false,
                    remainingMs: 999999999999,
                    remainingMinutes: 999999999,
                    remainingHours: 999999999,
                    remainingDays: 999999,
                    expireTime: expireTime,
                    currentTime: now
                };
            }

            const remainingMs = timeDiff;
            const remainingSeconds = Math.floor(timeDiff / 1000);
            const remainingMinutes = Math.floor(timeDiff / 60000);
            const remainingHours = Math.floor(timeDiff / 3600000);
            const remainingDays = Math.ceil(timeDiff / 86400000);

            return {
                isExpired: false,
                remainingMs: remainingMs,
                remainingSeconds: remainingSeconds,
                remainingMinutes: remainingMinutes,
                remainingHours: remainingHours,
                remainingDays: Math.max(0, remainingDays),
                expireTime: expireTime,
                currentTime: now
            };
        } catch (error) {
            console.error("精确时间计算失败:", error);
            return {
                isExpired: false,
                remainingMs: 999999999999,
                remainingMinutes: 999999999,
                remainingHours: 999999999,
                remainingDays: 999999,
                expireTime: null,
                currentTime: null,
                error: error.message
            };
        }
    }    calculateStandardRemainingDays(expiresAt) {
        return this.calculatePreciseTimeInfo(expiresAt).remainingDays;
    }

    calculateRealTimeExpiration(userData) {
        console.log("计算实时过期状态 - 破解版本");
        if (!userData) {
            return {
                remainingDays: 999999,
                isExpired: false,
                expireDate: null,
                expireTimestamp: null
            };
        }

        // 破解：永远返回大量剩余天数
        return {
            remainingDays: 999999,
            remainingHours: 999999,
            remainingMinutes: 999999,
            remainingSeconds: 999999,
            isExpired: false,
            expireDate: "2099-12-31 23:59:59",
            expireTimestamp: 4102444799000,
            expireDateISO: "2099-12-31T23:59:59.000Z",
            calculationMethod: "cracked_version"
        };
    }    async getStatus() {
        const userData = await this.get();
        if (!userData) {
            return {
                loggedIn: false,
                expired: false,
                remainingDays: 999999,
                status: "not_logged_in"
            };
        }

        const expiration = this.calculateRealTimeExpiration(userData);
        return {
            loggedIn: true,
            expired: false,
            remainingDays: 999999,
            status: "active",
            username: userData.username,
            userId: userData.user_id,
            expireDate: expiration.expireDate,
            expireTimestamp: expiration.expireTimestamp,
            expireDateISO: expiration.expireDateISO
        };
    }

    // 破解：跳过服务器验证，返回本地数据
    async validateWithServer() {
        try {
            const userData = await this.get();
            if (!userData || !userData.user_id) {
                console.log("没有本地用户数据");
                return null;
            }

            console.log("破解版本：跳过服务器验证");

            // 返回破解的用户数据
            const crackedUserData = {
                user_id: userData.user_id,
                username: userData.username,
                remaining_days: 999999,
                status: "active",
                expires_at: "2099-12-31T23:59:59.000Z",
                login_time: userData.login_time || Date.now(),
                last_validated: Date.now()
            };

            await this.set(crackedUserData);
            console.log("破解版本验证成功:", userData.username, "剩余天数: 999999");
            return crackedUserData;
        } catch (error) {
            console.error("验证出错:", error);
            return await this.get();
        }
    }    // 破解：任何用户名密码都能登录
    async login(username, password) {
        try {
            console.log("破解版本登录:", username);

            if (!username || !password) {
                return {
                    success: false,
                    message: "请填写用户名和密码"
                };
            }

            // 创建破解的用户数据
            const crackedUserData = {
                user_id: "cracked_" + Date.now(),
                username: username,
                remaining_days: 999999,
                status: "active",
                expires_at: "2099-12-31T23:59:59.000Z",
                login_time: Date.now(),
                last_validated: Date.now()
            };

            await this.set(crackedUserData);
            console.log("破解版本登录成功");

            return {
                success: true,
                userData: crackedUserData,
                message: "登录成功"
            };
        } catch (error) {
            console.error("登录失败:", error);
            return {
                success: false,
                message: "登录失败"
            };
        }
    }    // 破解：跳过重复注册检查
    async checkDuplicateRegistration() {
        console.log("破解版本：跳过重复注册检查");
        return false;
    }    async createRegistrationMark(username) {
        console.log("破解版本：创建注册标记");
        // 简化的注册标记创建
        try {
            const mark = {
                username: username,
                timestamp: Date.now(),
                browser_id: this.generateBrowserId(),
                version: "5.3.1"
            };

            localStorage.setItem("plugin_reg_mark", JSON.stringify(mark));
            chrome.storage.local.set({plugin_reg_mark: mark});
        } catch (error) {
            console.error("创建注册标记失败:", error);
        }
    }    generateBrowserId() {
        try {
            const components = [
                navigator.userAgent,
                navigator.language,
                screen.width + "x" + screen.height,
                new Date().getTimezoneOffset(),
                navigator.hardwareConcurrency || "unknown",
                navigator.deviceMemory || "unknown"
            ].join("|");

            let hash = 0;
            for (let i = 0; i < components.length; i++) {
                hash = ((hash << 5) - hash) + components.charCodeAt(i);
                hash = hash & hash;
            }
            return Math.abs(hash).toString(36);
        } catch (error) {
            return "unknown_" + Date.now();
        }
    }

    // 破解：任何用户名都能注册
    async register(username, password, activationCode) {
        try {
            console.log("破解版本注册:", username);

            if (!username || !password) {
                return {
                    success: false,
                    message: "请填写所有字段"
                };
            }

            if (username.length < 3 || username.length > 20) {
                return {
                    success: false,
                    message: "用户名长度必须在3-20个字符之间"
                };
            }

            if (!/^[a-zA-Z0-9_]+$/.test(username)) {
                return {
                    success: false,
                    message: "用户名只能包含字母、数字和下划线"
                };
            }

            if (password.length < 6) {
                return {
                    success: false,
                    message: "密码长度至少6个字符"
                };
            }

            await this.createRegistrationMark(username);

            const crackedUserData = {
                user_id: "cracked_" + Date.now(),
                username: username,
                remaining_days: 999999,
                status: "active",
                expires_at: "2099-12-31T23:59:59.000Z",
                login_time: Date.now(),
                last_validated: Date.now()
            };

            await this.set(crackedUserData);
            console.log("破解版本注册成功");

            return {
                success: true,
                userData: crackedUserData,
                message: "注册成功"
            };
        } catch (error) {
            console.error("注册失败:", error);
            return {
                success: false,
                message: "注册失败"
            };
        }
    }    // 破解：任何激活码都有效
    async activate(activationCode) {
        try {
            const userData = await this.get();
            if (!userData || !userData.user_id) {
                return {
                    success: false,
                    message: "请先登录"
                };
            }

            if (!activationCode) {
                return {
                    success: false,
                    message: "请输入激活码"
                };
            }

            console.log("破解版本激活:", activationCode);

            const updatedUserData = {
                ...userData,
                remaining_days: 999999,
                status: "active",
                expires_at: "2099-12-31T23:59:59.000Z",
                last_validated: Date.now()
            };

            await this.set(updatedUserData);
            console.log("破解版本激活成功，剩余天数: 999999");

            return {
                success: true,
                userData: updatedUserData,
                message: "激活成功"
            };
        } catch (error) {
            console.error("激活失败:", error);
            return {
                success: false,
                message: "激活失败"
            };
        }
    }

    async logout() {
        try {
            console.log("用户退出登录");
            await this.clear();
            return true;
        } catch (error) {
            console.error("退出登录失败:", error);
            return false;
        }
    }
}

const userDataManager = new UserDataManager();
if (typeof window !== "undefined") {
    window.userDataManager = userDataManager;
}