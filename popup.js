const script=document.createElement("script");async function checkUserStatusOnPopupOpen(){try{window.userDataManager||await new Promise(e=>setTimeout(e,100));return!await window.userDataManager.isExpired()||(console.log(1104),await disablePluginForExpiredUser(),showExpiredUserMessage(),!1)}catch(e){return console.error(1503),!0}}async function disablePluginForExpiredUser(){try{const e=await chrome.runtime.sendMessage({type:"HANDLE_EXPIRED_USER"});e&&e.success&&console.log("✅ 过期用户处理完成")}catch(e){console.error("处理过期用户失败:",e)}}function showExpiredUserMessage(){const e=document.querySelectorAll(".region-card"),t=document.getElementById("apply-btn"),o=document.getElementById("disable-btn"),n=document.getElementById("clean-augment-btn"),a=document.getElementById("tutorial-btn"),s=document.getElementById("mail-btn"),c=document.getElementById("buy-days-btn"),l=document.getElementById("region-toggle"),i=document.getElementById("logout-btn");e.forEach(e=>{e.style.opacity="0.5",e.style.pointerEvents="none"}),t&&(t.disabled=!0,t.style.opacity="0.5",t.style.cursor="not-allowed"),o&&(o.disabled=!0,o.style.opacity="0.5",o.style.cursor="not-allowed"),n&&(n.disabled=!1,n.style.opacity="1",n.style.cursor="pointer",n.title="清理Augment相关网站数据"),a&&(a.disabled=!0,a.style.opacity="0.5",a.style.cursor="not-allowed"),s&&(s.disabled=!0,s.style.opacity="0.5",s.style.cursor="not-allowed"),c&&(c.disabled=!0,c.style.opacity="0.5",c.style.cursor="not-allowed"),l&&(l.disabled=!0,l.style.opacity="0.5",l.style.cursor="not-allowed"),i&&(i.disabled=!0,i.style.opacity="0.5",i.style.cursor="not-allowed");const r=document.createElement("div");r.id="expired-notice",r.style.cssText="\n        background: #f8d7da;\n        color: #721c24;\n        border: 1px solid #f5c6cb;\n        border-radius: 4px;\n        padding: 15px;\n        margin-bottom: 15px;\n        text-align: center;\n        font-weight: bold;\n    ",r.innerHTML='\n        <div style="margin-bottom: 10px;">⚠️ 账户已过期</div>\n        <div style="font-size: 12px; font-weight: normal;">\n            您的账户使用期限已到期，所有功能已停用。<br>\n            请联系客服续费或重新购买。\n        </div>\n    ';const d=document.querySelector(".container")||document.body;d.insertBefore(r,d.firstChild)}script.src="user-data-manager.js",document.head.appendChild(script),document.addEventListener("DOMContentLoaded",async function(){if(!await checkUserStatusOnPopupOpen())return;const e=document.querySelectorAll(".region-card"),t=document.getElementById("apply-btn"),o=document.getElementById("disable-btn"),n=document.getElementById("clean-augment-btn"),a=document.getElementById("clean-status"),s=document.getElementById("tutorial-btn"),c=document.getElementById("mail-btn"),l=document.getElementById("buy-days-btn"),i=document.getElementById("region-toggle"),r=document.getElementById("region-content"),d=document.getElementById("author-text"),g=document.getElementById("current-location"),m=document.getElementById("current-time"),u=document.getElementById("current-details"),y=document.getElementById("status"),p=document.getElementById("loading"),h=document.getElementById("user-info"),w=document.getElementById("user-welcome"),f=document.getElementById("logout-btn"),b=document.getElementById("buy-days-modal"),E=document.getElementById("activation-input"),x=document.getElementById("activate-btn"),I=document.getElementById("cancel-btn"),v=document.getElementById("purchase-btn");let S=null;const C={US:{name:"美国",flag:"🇺🇸",timezones:["America/New_York","America/Chicago","America/Denver","America/Los_Angeles"],locale:"en-US",country:"United States"},TW:{name:"台湾",flag:"🇹🇼",timezones:["Asia/Taipei"],locale:"zh-TW",country:"Taiwan"},JP:{name:"日本",flag:"🇯🇵",timezones:["Asia/Tokyo"],locale:"ja-JP",country:"Japan"},SG:{name:"新加坡",flag:"🇸🇬",timezones:["Asia/Singapore"],locale:"en-SG",country:"Singapore"}};async function L(){try{window.userDataManager||await new Promise(e=>setTimeout(e,100)),console.log(1410);await window.userDataManager.get();console.log(1411);const e=await window.userDataManager.isLoggedIn();if(console.log(1412),e){const e=await window.userDataManager.validateWithServer();if(console.log(1413),e){console.log(1414),console.log("   输入数据:",e),console.log("   过期时间:",e.expires_at);const t=window.userDataManager.calculateRealTimeExpiration(e);console.log("   计算结果:",t);const o=t.remainingDays,n=t.isExpired;console.log("   提取的值:"),console.log(`     actualRemainingDays: ${o}`),console.log(`     isActuallyExpired: ${n}`);const a=n?"已过期":`剩余${o}天`;if(console.log("设置用户欢迎信息:",`欢迎，${e.username} (${a})`),console.log("📊 UI显示数据对比:"),console.log(`   服务器返回: ${e.remaining_days}天`),console.log(`   实时计算: ${o}天`),console.log("   精确过期判断: "+(n?"已过期":"未过期")),console.log(`   UI显示文本: ${a}`),console.log(`   显示逻辑: 使用精确过期判断 (!isActuallyExpired = ${!n})`),t.timeInfo&&!n){const e=t.timeInfo,o=Math.floor(e.remainingHours/24),n=e.remainingHours%24,a=e.remainingMinutes%60;o>0?console.log(`   精确剩余: ${o}天 ${n}小时 ${a}分钟`):n>0?console.log(`   精确剩余: ${n}小时 ${a}分钟`):console.log(`   精确剩余: ${a}分钟`)}if(w.textContent=`欢迎，${e.username} (${a})`,h.style.display="flex",n?(console.log("🚫 检测到用户账户已过期，自动停用插件功能"),await async function(){try{console.log("🚫 用户账户已过期，自动停用插件功能"),await chrome.storage.sync.remove(["selectedCity","selectedRegion"]),console.log("✅ 已清除sync storage中的地区设置");const tabs=await chrome.tabs.query({});for(const e of tabs)try{await chrome.scripting.executeScript({target:{tabId:e.id},func:()=>{localStorage.removeItem("globalTimezoneSpoofer_selectedCity"),localStorage.removeItem("globalTimezoneSpoofer_fingerprint"),console.log("🚫 插件功能已因账户过期而自动停用")}});try{await chrome.tabs.sendMessage(e.id,{type:"PLUGIN_DEACTIVATED"})}catch(e){}}catch(e){}_(!1);try{await chrome.runtime.sendMessage({type:"PLUGIN_DEACTIVATED"})}catch(e){console.warn("无法发送停用状态消息:",e)}console.log("✅ 插件功能已为所有标签页停用")}catch(e){console.error("❌ 停用插件功能失败:",e)}}(),F(),Y()):(V(),j()),o<7?(f.style.display="none",console.log("🔒 剩余天数少于7天，隐藏退出按钮")):f.style.display="block",console.log("✅ 用户信息已更新:",e),console.log(`🎯 用户 ${e.username} 剩余天数: ${o} 天 (统一标准计算)`),e.expires_at)try{const t=new Date(e.expires_at).toLocaleString();console.log(`📆 过期时间: ${t}`)}catch(t){console.log(`📆 过期时间: ${e.expires_at} (原始格式)`)}console.log(`📊 用户状态: ${e.status}`),console.log(`⏰ 登录时间: ${new Date(e.login_time).toLocaleString()}`)}else{console.log("⚠️ 服务器验证失败，尝试使用本地数据");const e=await window.userDataManager.get();if(console.log("本地用户数据:",e),e&&e.username){const t=window.userDataManager.calculateRealTimeExpiration(e),o=t.remainingDays,n=t.isExpired,a=n?"已过期":`剩余${o}天`;if(console.log("使用本地数据设置用户信息:",`欢迎，${e.username} (${a})`),console.log("📊 本地数据对比:"),console.log(`   存储数据: ${e.remaining_days}天`),console.log(`   实时计算: ${o}天`),console.log("   精确过期判断: "+(n?"已过期":"未过期")),console.log(`   UI显示文本: ${a}`),console.log(`   显示逻辑: 使用精确过期判断 (!isActuallyExpired = ${!n})`),t.timeInfo&&!n){const e=t.timeInfo,o=Math.floor(e.remainingHours/24),n=e.remainingHours%24,a=e.remainingMinutes%60;o>0?console.log(`   精确剩余: ${o}天 ${n}小时 ${a}分钟`):n>0?console.log(`   精确剩余: ${n}小时 ${a}分钟`):console.log(`   精确剩余: ${a}分钟`)}if(w.textContent=`欢迎，${e.username} (${a})`,h.style.display="flex",n?(F(),Y()):(V(),j()),f.style.display=o<7?"none":"block",console.log(1206),t.expireDate)console.log(1207);else if(e.expires_at)try{new Date(e.expires_at).toLocaleString();console.log(1207)}catch(e){console.log(1207)}console.log(1420),console.log(1421)}else console.log(1422),F(),Y()}}else console.log(1423),Y()}catch(e){console.error("加载用户信息失败:",e),Y()}}function T(){e.forEach(e=>{e.classList.remove("selected"),e.dataset.region===S&&e.classList.add("selected")})}function $(e){if(e.selectedCity&&e.selectedRegion){const t=C[e.selectedRegion];g.textContent=`${t.flag} ${e.selectedCity.city}, ${e.selectedCity.country}`,u.textContent=`时区: ${e.selectedCity.timezone} | 语言: ${e.selectedCity.locale}`,y.classList.add("active")}}async function D(){if(t.disabled)alert("您的账户已过期，无法使用此功能。请点击上面小钻石图标充值。");else if(S){A(!0);try{await chrome.runtime.sendMessage({type:"PLUGIN_WORKING"})}catch(e){console.warn("无法发送工作状态消息:",e)}try{const e=C[S],o=e.timezones[Math.floor(Math.random()*e.timezones.length)],n=function(e,t,o){const n={US:["New York","Los Angeles","Chicago","Houston","Phoenix"],TW:["Taipei","Kaohsiung","Taichung","Tainan","Taoyuan"],JP:["Tokyo","Osaka","Yokohama","Nagoya","Sapporo"],SG:["Singapore","Jurong West","Woodlands","Tampines","Sengkang"]}[e],a={US:{lat:40.7128,lng:-74.006},TW:{lat:25.033,lng:121.5654},JP:{lat:35.6762,lng:139.6503},SG:{lat:1.3521,lng:103.8198}}[e];return{city:n[Math.floor(Math.random()*n.length)],state:"US"===e?"NY":o.name,country:o.country,country_code:e,timezone:t,locale:o.locale,lat:a.lat+.2*(Math.random()-.5),lng:a.lng+.2*(Math.random()-.5)}}(S,o,e);await chrome.storage.sync.set({selectedRegion:S,selectedCity:n,lastUpdate:Date.now()});try{console.log("🌍 通过background script应用设置到所有标签页..."),await chrome.runtime.sendMessage({type:"APPLY_PLUGIN_SETTINGS",cityData:n}),console.log("✅ 设置已通过background script应用到所有标签页")}catch(e){console.warn("通过background script应用设置失败，使用备用方法:",e);const t="globalTimezoneSpoofer_selectedCity",tabs=await chrome.tabs.query({});for(const e of tabs)try{await chrome.scripting.executeScript({target:{tabId:e.id},func:(key,e)=>{localStorage.setItem(key,JSON.stringify(e)),localStorage.removeItem("globalTimezoneSpoofer_fingerprint"),console.log("🔄 Updated localStorage and cleared fingerprint for new settings"),console.log("✅ Plugin will auto-activate with new region settings")},args:[t,n]})}catch(e){}}if(await new Promise(e=>setTimeout(e,500)),$({selectedRegion:S,selectedCity:n}),confirm("设置已应用到所有标签页！是否刷新现有标签页以立即生效？\n（新打开的标签页会自动应用设置）")){const tabs=await chrome.tabs.query({});for(const e of tabs)try{e.url.startsWith("chrome://")||e.url.startsWith("chrome-extension://")||await chrome.tabs.reload(e.id)}catch(e){}}A(!1),_(!0),setTimeout(async()=>{try{await chrome.runtime.sendMessage({type:"PLUGIN_ACTIVATED"})}catch(e){console.warn("无法发送激活状态消息:",e)}},1e3),function(){const e=t.textContent;t.textContent="✅ 设置已应用",t.style.background="#4CAF50",setTimeout(()=>{t.textContent=e,t.style.background=""},2e3)}()}catch(e){console.error("Failed to apply settings:",e),alert("应用设置失败，请重试"),A(!1)}}else alert("请先选择一个地区")}async function k(){if(confirm('确定要停用插件功能吗？停用后需要重新点击"应用设置"来启用。')){A(!0);try{await chrome.storage.sync.remove(["selectedCity","selectedRegion"]),console.log("✅ 已清除sync storage中的地区设置");try{await chrome.storage.session.set({plugin_globally_activated:!1}),console.log("✅ 全局激活状态已清除")}catch(e){console.log("⚠️ chrome.storage.session不可用")}const tabs=await chrome.tabs.query({});for(const e of tabs)try{await chrome.scripting.executeScript({target:{tabId:e.id},func:()=>{localStorage.removeItem("globalTimezoneSpoofer_selectedCity"),localStorage.removeItem("globalTimezoneSpoofer_fingerprint"),console.log("⏸️ Plugin disabled by removing region settings")}});try{await chrome.tabs.sendMessage(e.id,{type:"PLUGIN_DEACTIVATED"})}catch(e){}}catch(e){}_(!1);try{await chrome.runtime.sendMessage({type:"PLUGIN_DEACTIVATED"})}catch(e){console.warn("无法发送停用状态消息:",e)}A(!1),alert("插件功能已停用")}catch(e){console.error("Failed to disable plugin:",e),alert("停用插件失败，请重试"),A(!1)}}}function _(e){e?(t.style.display="inline-block",o.style.display="inline-block",t.textContent="重新应用设置 / Reapply Settings"):(t.style.display="inline-block",o.style.display="none",t.textContent="应用设置 / Apply Settings")}function A(e){e?(p.style.display="block",t.disabled=!0):(p.style.display="none",t.disabled=!1)}function M(){const now=new Date;m.textContent=now.toLocaleTimeString("zh-CN",{hour12:!1,hour:"2-digit",minute:"2-digit",second:"2-digit"})}async function B(){try{U("正在清理Augment相关数据...","progress"),n.disabled=!0;const e={origins:["https://*.augmentcode.com","http://*.augmentcode.com","https://*.augment.com","http://*.augment.com","https://augmentcode.com","http://augmentcode.com","https://augment.com","http://augment.com"]},t=[];t.push(chrome.browsingData.removeCookies(e).catch(e=>console.warn("清理Cookies失败:",e))),t.push(chrome.browsingData.removeLocalStorage(e).catch(e=>console.warn("清理LocalStorage失败:",e))),t.push(chrome.browsingData.removeCache(e).catch(e=>console.warn("清理Cache失败:",e))),t.push(chrome.browsingData.removeIndexedDB(e).catch(e=>console.warn("清理IndexedDB失败:",e))),t.push(chrome.browsingData.removeWebSQL(e).catch(e=>console.warn("清理WebSQL失败:",e))),await Promise.all(t);try{const tabs=await chrome.tabs.query({});for(const e of tabs)if(e.url&&(e.url.includes("augmentcode.com")||e.url.includes("augment.com")))try{await chrome.scripting.executeScript({target:{tabId:e.id},func:()=>{try{localStorage.clear(),sessionStorage.clear(),console.log("🧹 页面存储数据已清理")}catch(e){console.warn("清理页面存储失败:",e)}}})}catch(e){}}catch(e){console.warn("清理标签页数据失败:",e)}U("✅ Augment数据清理完成！","success"),setTimeout(()=>{P()},3e3)}catch(e){console.error("清理Augment数据失败:",e),U("❌ 清理失败，请重试","error"),setTimeout(()=>{P()},3e3)}finally{n.disabled=!1}}function U(e,type){a.style.display="block",a.className=`clean-status ${type}`,a.innerHTML="progress"===type?`<div class="clean-progress">${e}</div>`:`<div>${e}</div>`}function P(){a.style.display="none"}function z(){chrome.tabs.create({url:"https://xoxome.online/?page_id=1685"})}function N(){chrome.tabs.create({url:"https://mail.xoxome.online"})}function R(){b.style.display="flex",E.addEventListener("input",()=>{const e=E.value.trim();x.disabled=!e}),x.addEventListener("click",H),v.addEventListener("click",W),I.addEventListener("click",G),b.addEventListener("click",e=>{e.target===b&&G()}),E.addEventListener("keypress",e=>{"Enter"!==e.key||x.disabled||H()})}function G(){b.style.display="none",E.value="",x.disabled=!0}async function H(){const e=E.value.trim();if(e){x.disabled=!0,x.textContent="激活中...";try{const t=await window.userDataManager.get();if(!t||!t.user_id)return void alert("请先登录");const o=await window.userDataManager.activate(e);o.success?(alert(`激活成功！增加了${o.userData.remaining_days}天使用时间`),await chrome.runtime.sendMessage({type:"AUTH_STATUS_CHANGE",authorized:!0,userData:o.userData}),G(),await L()):alert(o.message||"激活失败")}catch(e){console.error("激活失败:",e),alert("激活失败，请检查网络连接")}finally{x.disabled=!1,x.textContent="立即激活"}}}async function W(){try{const e=await fetch("http://103.96.75.196/api/get_config.php?key=purchase_link"),t=await e.json();let o="https://xoxome.online/?page_id=1685";t.success&&t.data&&t.data.config_value&&(o=t.data.config_value),chrome.tabs.create({url:o}),G()}catch(e){console.error("获取购买链接失败:",e),chrome.tabs.create({url:"https://xoxome.online/?page_id=1685"}),G()}}function q(){i.classList.contains("collapsed")?(i.classList.remove("collapsed"),r.classList.remove("collapsed"),r.classList.add("expanded")):(i.classList.add("collapsed"),r.classList.remove("expanded"),r.classList.add("collapsed"))}function O(){chrome.tabs.create({url:"https://browserleaks.com/webrtc"})}async function J(){if(confirm("确定要退出登录吗？"))try{await window.userDataManager.logout(),await chrome.runtime.sendMessage({type:"AUTH_STATUS_CHANGE",authorized:!1}),window.location.href="auth.html"}catch(e){console.error("退出登录失败:",e),alert("退出登录失败，请重试")}}function F(){t&&(t.disabled=!0,t.style.opacity="0.5",t.style.cursor="not-allowed",t.textContent="账户已过期 / Account Expired",console.log("🚫 应用设置按钮已禁用（账户过期）"))}function V(){t&&(t.disabled=!1,t.style.opacity="1",t.style.cursor="pointer",t.textContent="应用设置 / Apply Settings",console.log("✅ 应用设置按钮已启用"))}function Y(){o&&(o.disabled=!0,o.style.opacity="0.5",o.style.cursor="not-allowed",o.title="账户已过期或未登录，功能不可用",console.log("🚫 停用按钮已禁用"))}function j(){o&&(o.disabled=!1,o.style.opacity="1",o.style.cursor="pointer",o.title="",console.log("✅ 停用按钮已启用"))}!async function(){console.log(1401),console.log(1402),console.log(1403),console.log(1404),console.log(1405),console.log(1406),await async function(){try{const e=await chrome.storage.sync.get(["selectedRegion","selectedCity","lastUpdate"]);e.selectedRegion?(S=e.selectedRegion,T(),$(e)):(S="US",T(),g.textContent="未设置",u.textContent="请选择一个地区并应用设置")}catch(e){console.error("Failed to load settings:",e),g.textContent="加载失败",u.textContent="无法加载当前设置"}}(),await L(),e.forEach(e=>{e.addEventListener("click",()=>{S=e.dataset.region,T()})}),t.addEventListener("click",D),o.addEventListener("click",k),n.addEventListener("click",B),s.addEventListener("click",z),c.addEventListener("click",N),l.addEventListener("click",R),i.addEventListener("click",q),d.addEventListener("dblclick",O),f.addEventListener("click",J),await async function(){try{(await chrome.storage.sync.get(["selectedCity"])).selectedCity?_(!0):_(!1)}catch(e){_(!1)}}(),M(),setInterval(M,1e3)}()});