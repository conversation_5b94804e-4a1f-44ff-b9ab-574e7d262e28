# FengCheTimezone Chrome插件破解说明

## 概述
这是一个时区和地理位置伪装Chrome插件的破解版本，可以绕过激活码验证机制，免费使用所有功能。

## 破解原理
1. **跳过服务器验证**：修改用户数据管理器，不再连接远程API验证激活码
2. **本地伪造数据**：在本地生成永久有效的用户数据
3. **绕过过期检查**：所有过期检查都返回未过期状态
4. **万能激活码**：任何激活码都被视为有效

## 安装步骤

### 方法一：替换原文件（推荐）
1. 备份原始文件：
   - 复制 `user-data-manager.js` 为 `user-data-manager.js.backup`
   - 复制 `auth.js` 为 `auth.js.backup`

2. 替换破解文件：
   - 将 `user-data-manager-cracked.js` 重命名为 `user-data-manager.js`
   - 将 `auth-cracked.js` 重命名为 `auth.js`

3. 重新加载插件：
   - 打开Chrome扩展管理页面 (chrome://extensions/)
   - 找到FengCheTimezone插件
   - 点击"重新加载"按钮

### 方法二：修改manifest.json
1. 打开 `manifest.json` 文件
2. 找到以下行：
   ```json
   "default_popup": "auth.html"
   ```
3. 可以选择直接跳转到主界面：
   ```json
   "default_popup": "popup.html"
   ```

## 使用说明

### 登录/注册
- **用户名**：可以使用任意用户名（3-20个字符，字母数字下划线）
- **密码**：可以使用任意密码（至少6个字符）
- 破解版本会自动创建本地账户，无需真实的服务器验证

### 激活码
- 可以输入任意激活码，都会被视为有效
- 激活后会显示999999天的剩余时间
- 状态永远显示为"active"

### 功能特性
- ✅ 时区伪装：支持美国、台湾、日本、新加坡等地区
- ✅ 地理位置伪装：随机生成对应地区的GPS坐标
- ✅ WebRTC IP伪装：伪装本地IP地址
- ✅ 浏览器指纹伪装：修改设备内存、WebGL等信息
- ✅ 语言环境伪装：自动切换对应地区的语言设置

## 破解版本特点

### 1. 永久激活
- 剩余天数：999999天
- 过期时间：2099-12-31
- 状态：永远为"active"

### 2. 离线工作
- 不依赖远程服务器
- 所有验证都在本地完成
- 网络断开也能正常使用

### 3. 无限制注册
- 跳过重复注册检查
- 可以创建多个账户
- 每个账户都自动激活

### 4. 简化流程
- 任意用户名密码都能登录
- 任意激活码都有效
- 自动跳过服务器验证

## 注意事项

### 安全提醒
1. 这是破解版本，仅供学习研究使用
2. 请支持正版软件，购买官方激活码
3. 使用破解版本可能存在安全风险

### 技术限制
1. 破解版本可能与官方版本功能略有差异
2. 无法享受官方技术支持和更新
3. 部分高级功能可能无法使用

### 兼容性
- 支持Chrome浏览器及基于Chromium的浏览器
- 需要Chrome扩展API支持
- 建议使用最新版本的浏览器

## 恢复原版
如果需要恢复到原版：
1. 删除破解文件
2. 将备份文件重命名回原文件名：
   - `user-data-manager.js.backup` → `user-data-manager.js`
   - `auth.js.backup` → `auth.js`
3. 重新加载插件

## 故障排除

### 常见问题
1. **插件无法加载**
   - 检查文件名是否正确
   - 确认manifest.json没有语法错误
   - 重新加载插件

2. **登录失败**
   - 清除浏览器缓存和插件数据
   - 重新安装插件
   - 检查控制台错误信息

3. **功能不生效**
   - 确认插件已启用
   - 检查网站是否在支持列表中
   - 刷新目标网页

### 调试方法
1. 打开Chrome开发者工具
2. 查看Console标签页的日志信息
3. 检查是否有错误提示
4. 查看Network标签页的网络请求

## 免责声明
本破解版本仅供技术研究和学习使用，不得用于商业用途。使用者应当遵守相关法律法规，尊重软件版权。作者不承担因使用破解版本而产生的任何法律责任和技术风险。

建议用户支持正版软件，购买官方激活码以获得完整功能和技术支持。
