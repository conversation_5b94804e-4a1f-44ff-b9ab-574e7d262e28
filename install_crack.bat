@echo off
chcp 65001 >nul
echo ========================================
echo   FengCheTimezone 插件破解安装脚本
echo ========================================
echo.

:: 检查是否存在原始文件
if not exist "user-data-manager.js" (
    echo ❌ 错误：找不到 user-data-manager.js 文件
    echo 请确保在插件目录中运行此脚本
    pause
    exit /b 1
)

if not exist "auth.js" (
    echo ❌ 错误：找不到 auth.js 文件
    echo 请确保在插件目录中运行此脚本
    pause
    exit /b 1
)

if not exist "user-data-manager-cracked.js" (
    echo ❌ 错误：找不到破解文件 user-data-manager-cracked.js
    echo 请确保破解文件在同一目录中
    pause
    exit /b 1
)

if not exist "auth-cracked.js" (
    echo ❌ 错误：找不到破解文件 auth-cracked.js
    echo 请确保破解文件在同一目录中
    pause
    exit /b 1
)

echo 📁 检测到以下文件：
echo    ✅ user-data-manager.js
echo    ✅ auth.js
echo    ✅ user-data-manager-cracked.js
echo    ✅ auth-cracked.js
echo.

:: 询问用户是否继续
set /p choice="是否继续安装破解版本？(Y/N): "
if /i "%choice%" neq "Y" (
    echo 安装已取消
    pause
    exit /b 0
)

echo.
echo 🔄 开始安装破解版本...
echo.

:: 备份原始文件
echo 📦 备份原始文件...
if not exist "user-data-manager.js.backup" (
    copy "user-data-manager.js" "user-data-manager.js.backup" >nul
    if errorlevel 1 (
        echo ❌ 备份 user-data-manager.js 失败
        pause
        exit /b 1
    )
    echo    ✅ user-data-manager.js → user-data-manager.js.backup
) else (
    echo    ⚠️  备份文件已存在，跳过备份 user-data-manager.js
)

if not exist "auth.js.backup" (
    copy "auth.js" "auth.js.backup" >nul
    if errorlevel 1 (
        echo ❌ 备份 auth.js 失败
        pause
        exit /b 1
    )
    echo    ✅ auth.js → auth.js.backup
) else (
    echo    ⚠️  备份文件已存在，跳过备份 auth.js
)

:: 替换为破解版本
echo.
echo 🔧 安装破解文件...
copy "user-data-manager-cracked.js" "user-data-manager.js" >nul
if errorlevel 1 (
    echo ❌ 替换 user-data-manager.js 失败
    pause
    exit /b 1
)
echo    ✅ user-data-manager-cracked.js → user-data-manager.js

copy "auth-cracked.js" "auth.js" >nul
if errorlevel 1 (
    echo ❌ 替换 auth.js 失败
    pause
    exit /b 1
)
echo    ✅ auth-cracked.js → auth.js

echo.
echo 🎉 破解版本安装完成！
echo.
echo 📋 接下来的步骤：
echo    1. 打开 Chrome 浏览器
echo    2. 访问 chrome://extensions/
echo    3. 找到 FengCheTimezone 插件
echo    4. 点击"重新加载"按钮
echo    5. 使用任意用户名密码登录
echo    6. 输入任意激活码激活
echo.
echo 💡 使用说明：
echo    - 用户名：任意（3-20字符，字母数字下划线）
echo    - 密码：任意（至少6个字符）
echo    - 激活码：任意字符串都有效
echo    - 剩余天数：999999天
echo.
echo ⚠️  注意事项：
echo    - 这是破解版本，仅供学习研究使用
echo    - 请支持正版软件，购买官方激活码
echo    - 如需恢复原版，运行 restore_original.bat
echo.
pause
